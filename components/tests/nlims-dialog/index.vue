<template>
  <div>
    <TransitionRoot appear :show="show" as="template">
      <Dialog as="div" class="relative z-10">
        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
          leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center text-center">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95">
              <DialogPanel
                class="w-full max-w-7xl m-20 transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">
                <div class="border-b px-3 py-3 flex items-center justify-between">
                  <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                    <img src="@/assets/icons/medical_records.svg" class="w-8 h-8 mr-2" />
                    NLIMS Test Details
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <div class="grid grid-cols-3 gap-4 px-5 py-5">
                  <div class="rounded border">
                    <div class="px-4 py-2 bg-gray-50 border-b">
                      <h3 class="text-lg font-semibold text-gray-600">
                        Patient
                      </h3>
                    </div>
                    <div class="w-full space-y-2 py-2">
                      <div class="w-full flex justify-between px-5 py-2">
                        <h3 class="font-semibold">Patient Number</h3>
                        <p>{{ data.patient.national_patient_id }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Name</h3>
                        <p>
                          {{
                            capitalize(
                              `${data.patient.first_name} ${data.patient.last_name}`
                            )
                          }}
                        </p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2">
                        <h3 class="font-semibold">Sex</h3>
                        <p>{{ data.patient.gender }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Age</h3>
                        <p>{{ calculateAge(data.patient.date_of_birth) }}</p>
                      </div>
                    </div>
                  </div>
                  <div class="rounded border">
                    <div class="px-4 py-2 bg-gray-50 border-b">
                      <h3 class="text-lg font-semibold text-gray-600">
                        Specimen
                      </h3>
                    </div>
                    <div class="w-full space-y-2 py-2">
                      <div class="w-full flex justify-between px-5 py-2">
                        <h3 class="font-semibold">Specimen Type</h3>
                        <p>{{ data.order.sample_type.preferred_name }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Tracking Number</h3>
                        <p>{{ data.order.tracking_number }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2">
                        <h3 class="font-semibold">Order UUID</h3>
                        <p>{{ data.order.uuid }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Status</h3>
                        <p>{{ data.order.sample_status.name }}</p>
                      </div>
                    </div>
                  </div>
                  <div class="rounded border max-h-72 overflow-y-auto">
                    <div class="px-4 py-2 bg-gray-50 border-b">
                      <h3 class="text-lg font-semibold text-gray-600">Test</h3>
                    </div>
                    <div class="w-full space-y-2 py-2">
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Tests</h3>
                        <div class="text-right">
                          <p v-for="(test, index) in data.tests" :key="index" class="mb-1">
                            {{ test.test_type.preferred_name }} ({{ test.test_status }})
                          </p>
                        </div>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2">
                        <h3 class="font-semibold">Date Created</h3>
                        <p>
                          {{
                            moment(data.order.date_created).format(DATE_FORMAT)
                          }}
                        </p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Priority</h3>
                        <p>{{ data.order.priority }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2">
                        <h3 class="font-semibold">Ward/Location</h3>
                        <p>{{ data.order.order_location }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Sending Facility</h3>
                        <p>{{ data.order.sending_facility }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2">
                        <h3 class="font-semibold">Drawn By</h3>
                        <p>{{ data.order.drawn_by.name }}</p>
                      </div>
                      <div class="w-full flex justify-between px-5 py-2 bg-gray-50 border-t border-b border-dotted">
                        <h3 class="font-semibold">Requested By</h3>
                        <p>{{ data.order.requested_by }}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mx-5 rounded border mb-5">
                  <div class="flex items-center justify-between bg-gray-50 px-4 py-2 border-b rounded-t">
                    <h3 class="text-lg font-semibold text-gray-600">Test Results</h3>
                    <div class="justify-end flex items-center space-x-3">
                      <CoreActionButton :loading="loading" :icon="arrowIcon" color="success" text="Proceed"
                        :click="mergeOrder" />
                    </div>
                  </div>

                  <!-- Display results for each test -->
                  <div v-for="(test, testIndex) in data.tests" :key="testIndex"
                    class="border-b border-gray-200 last:border-b-0">
                    <div class="bg-gray-100 px-4 py-2 border-b">
                      <h4 class="font-semibold text-gray-700">{{ test.test_type.preferred_name }}</h4>
                      <p class="text-sm text-gray-600">Status: {{ test.test_status }} | Updated: {{
                        moment(test.time_updated).format(DATE_FORMAT) }}</p>
                    </div>

                    <!-- Test Results -->
                    <div v-if="test.test_results && test.test_results.length > 0" class="px-4 py-2">
                      <div v-for="(result, resultIndex) in test.test_results" :key="resultIndex"
                        class="flex justify-between items-center py-2 border-b border-dotted last:border-b-0">
                        <div>
                          <h5 class="font-medium">{{ result.measure.preferred_name }}</h5>
                          <p class="text-sm text-gray-600">{{ result.measure.name }}</p>
                        </div>
                        <div class="text-right">
                          <p><span class="font-semibold">{{ result.result.value }}</span> {{ result.result.unit }}</p>
                          <p class="text-sm text-gray-500">{{ moment(result.result.result_date).format(DATE_FORMAT) }}
                          </p>
                        </div>
                      </div>
                    </div>

                    <!-- No results message -->
                    <div v-else class="px-4 py-3 text-center text-gray-500">
                      No results available for this test
                    </div>
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  XMarkIcon,
  ArrowLongRightIcon,
} from "@heroicons/vue/24/solid/index.js";
import moment from "moment";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Request, Response } from "@/types";
import { useAuthStore } from "@/store/auth";
import { DATE_FORMAT, ERROR_MESSAGE } from "@/utils/constants";
import { calculateAge, capitalize } from "@/utils/functions";

interface SampleType {
  id: number;
  name: string;
  description: string;
  preferred_name: string;
  scientific_name: string;
}

interface SampleStatus {
  id: number;
  name: string;
  short_name?: string;
}

interface DrawnBy {
  id: string;
  name: string;
  phone_number: string;
}

interface Order {
  uuid: string;
  tracking_number: string;
  sample_type: SampleType;
  sample_status: SampleStatus;
  order_location: string;
  date_created: string;
  priority: string;
  reason_for_test: string;
  drawn_by: DrawnBy;
  target_lab: string;
  sending_facility: string;
  district: string;
  site_code_number: string;
  requested_by: string;
  art_start_date?: string;
  arv_number: string;
  art_regimen: string;
  clinical_history?: string;
  lab_location: string;
  source_system: string;
  status_trail: any[];
}

interface Patient {
  id: number;
  national_patient_id: string;
  first_name: string;
  last_name: string;
  gender: string;
  date_of_birth: string;
  address?: string;
  email?: string;
  phone_number?: string;
}

interface TestType {
  id: number;
  test_category_id: number;
  name: string;
  short_name: string;
  targetTAT: string;
  description: string;
  preferred_name: string;
  scientific_name: string;
}

interface StatusTrail {
  status_id: number;
  status: string;
  timestamp: string;
  updated_by: {
    first_name: string;
    last_name: string;
    id: string;
    phone_number: string;
  };
}

interface Measure {
  name: string;
  nlims_code: string;
  preferred_name: string;
  scientific_name: string;
  short_name: string;
  measure_type: string;
}

interface Result {
  value: string;
  unit: string;
  result_date: string;
  platform: string;
  platformserial: string;
}

interface TestResult {
  measure: Measure;
  result: Result;
}

interface Test {
  tracking_number: string;
  arv_number: string;
  uuid: string;
  test_status: string;
  time_updated: string;
  test_type: TestType;
  status_trail: StatusTrail[];
  test_results: TestResult[];
}

interface NLIMSData {
  order: Order;
  patient: Patient;
  tests: Test[];
}

interface Props {
  data: NLIMSData;
  open: boolean;
  callback: (accessionNumber: string) => void;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'update', value: boolean): void;
}>();

const show = ref<boolean>(props.open || false);
const arrowIcon = <Object>ArrowLongRightIcon;
const loading = ref<boolean>(false);
const authStore = useAuthStore();

const handleClick = (): void => {
  show.value = !show.value;
};



const mergeOrder = async (): Promise<void> => {
  loading.value = true;

  const request: Request = {
    route: endpoints.mergeOrder,
    method: "POST",
    body: {
      ...props.data,
      lab_location: authStore.locations.find((location) => location.name == authStore.selectedLocation)?.id
    },
  };

  const { data, error, pending }: Response = await fetchRequest(request);

  loading.value = pending;

  if (data.value) {
    useNuxtApp().$toast.success("Order merged successfully!");
    props.callback(data.value.accession_number);
    loading.value = false;
    emit("update", true);
    handleClick();
  }

  if (error.value) {
    loading.value = false;
    useNuxtApp().$toast.error(ERROR_MESSAGE);
    console.error(error.value);
    handleClick();
  }
};
</script>

<style></style>
